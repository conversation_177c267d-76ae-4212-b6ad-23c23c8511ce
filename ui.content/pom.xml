<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2015 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>com.adobe.training</groupId>
        <artifactId>wetrain</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N                                   -->
    <!-- ====================================================================== -->
    <artifactId>wetrain.ui.content</artifactId>
    <packaging>content-package</packaging>
    <name>We.Train - UI content</name>
    <description>UI content package for We.Train</description>

    <!-- ====================================================================== -->
    <!-- B U I L D   D E F I N I T I O N                                        -->
    <!-- ====================================================================== -->
    <build>
        <plugins>
            <!-- ====================================================================== -->
            <!-- V A U L T   P A C K A G E   P L U G I N S                              -->
            <!-- ====================================================================== -->
            <plugin>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>filevault-package-maven-plugin</artifactId>
                <configuration>
                    <properties>
                        <cloudManagerTarget>none</cloudManagerTarget>
                    </properties>
                    <group>com.adobe.training</group>
                    <name>wetrain.ui.content</name>
                    <packageType>content</packageType>
                    <accessControlHandling>merge</accessControlHandling>
                    <validatorsSettings>
                        <jackrabbit-filter>
                            <options>
                                <validRoots>/conf,/content,/content/experience-fragments,/content/dam</validRoots>
                            </options>
                        </jackrabbit-filter>
                    </validatorsSettings>
                    <dependencies>
                        <dependency>
                            <groupId>com.adobe.training</groupId>
                            <artifactId>wetrain.ui.apps</artifactId>
                            <version>${project.version}</version>
                        </dependency>
                    </dependencies>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.day.jcr.vault</groupId>
                <artifactId>content-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <verbose>true</verbose>
                    <failOnError>true</failOnError>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- ====================================================================== -->
    <!-- D E P E N D E N C I E S                                                -->
    <!-- ====================================================================== -->
    <dependencies>
        <dependency>
            <groupId>com.adobe.training</groupId>
            <artifactId>wetrain.core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.adobe.training</groupId>
            <artifactId>wetrain.ui.apps</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.aem</groupId>
            <artifactId>aem-sdk-api</artifactId>
        </dependency>
    </dependencies>
</project>
