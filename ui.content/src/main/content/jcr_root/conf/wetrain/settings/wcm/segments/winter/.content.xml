<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="cq:Page">
    <jcr:content
        jcr:primaryType="cq:PageContent"
        jcr:title="Winter"
        sling:resourceType="cq/contexthub/components/segment-page"
        segmentBoost="{Long}0"
        segmentName="Season Winter">
        <traits
            jcr:primaryType="nt:unstructured"
            sling:resourceType="cq/contexthub/components/traits/logic/operator-or">
            <orpar
                jcr:primaryType="nt:unstructured"
                sling:resourceType="foundation/components/parsys">
                <one
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/logic/operator-and">
                    <andpar
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="foundation/components/parsys">
                        <location
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="greater-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="40"/>
                        </location>
                        <dates
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/logic/operator-or">
                            <orpar
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="foundation/components/parsys">
                                <afterMonth
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                                    dataType="number"
                                    operator="greater-than-or-equal">
                                    <left
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                        property="surferinfo/month"/>
                                    <right
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                        value="9"/>
                                </afterMonth>
                                <beforeMonth
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                                    dataType="number"
                                    operator="less-than">
                                    <left
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                        property="surferinfo/month"/>
                                    <right
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                        value="4"/>
                                </beforeMonth>
                            </orpar>
                        </dates>
                    </andpar>
                </one>
                <two
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/logic/operator-and">
                    <andpar
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="foundation/components/parsys">
                        <location
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="less-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="-38"/>
                        </location>
                        <fromMonth
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="greater-than-or-equal">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="surferinfo/month"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="4"/>
                        </fromMonth>
                        <toMonth
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="less-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="surferinfo/month"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="9"/>
                        </toMonth>
                    </andpar>
                </two>
                <three
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                    dataType="number"
                    operator="greater-than">
                    <left
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                        property="geolocation/latitude"/>
                    <right
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                        value="70"/>
                </three>
                <four
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                    dataType="number"
                    operator="less-than">
                    <left
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                        property="geolocation/latitude"/>
                    <right
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                        value="-50"/>
                </four>
            </orpar>
        </traits>
    </jcr:content>
</jcr:root>
