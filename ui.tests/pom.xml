<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2020 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->

<!--
 | DO NOT MODIFY
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.adobe.training</groupId>
        <artifactId>wetrain</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>wetrain.ui.tests</artifactId>
    <name>We.Train - UI Tests</name>
    <description>UI Tests for We.Train</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <node.version>v12.16.3</node.version>
        <npm.version>6.14.4</npm.version>

        <HEADLESS_BROWSER>false</HEADLESS_BROWSER>
        <REPORTS_PATH>${project.basedir}/target/reports</REPORTS_PATH>

        <!-- AEMaaCS UI testing convention properties -->
        <AEM_AUTHOR_URL>http://localhost:4502</AEM_AUTHOR_URL>
        <AEM_AUTHOR_USERNAME>admin</AEM_AUTHOR_USERNAME>
        <AEM_AUTHOR_PASSWORD>admin</AEM_AUTHOR_PASSWORD>
        <AEM_PUBLISH_URL />
        <AEM_PUBLISH_USERNAME>admin</AEM_PUBLISH_USERNAME>
        <AEM_PUBLISH_PASSWORD>admin</AEM_PUBLISH_PASSWORD>
        <SELENIUM_BROWSER>chrome</SELENIUM_BROWSER>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-jar</id>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>${project.basedir}/assembly-ui-test-docker-context.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Profile to run UI tests locally locally -->
        <profile>
            <id>ui-tests-local-execution</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>${frontend-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>install node and npm</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <configuration>
                                    <nodeVersion>${node.version}</nodeVersion>
                                    <npmVersion>${npm.version}</npmVersion>
                                    <installDirectory>test-module</installDirectory>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm ci</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>test chrome</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>run test-local-${SELENIUM_BROWSER}</arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <workingDirectory>test-module</workingDirectory>
                            <environmentVariables>
                                <AEM_AUTHOR_URL>${AEM_AUTHOR_URL}</AEM_AUTHOR_URL>
                                <AEM_AUTHOR_USERNAME>${AEM_AUTHOR_USERNAME}</AEM_AUTHOR_USERNAME>
                                <AEM_AUTHOR_PASSWORD>${AEM_AUTHOR_PASSWORD}</AEM_AUTHOR_PASSWORD>
                                <AEM_PUBLISH_URL>${AEM_PUBLISH_URL}</AEM_PUBLISH_URL>
                                <AEM_PUBLISH_USERNAME>${AEM_PUBLISH_USERNAME}</AEM_PUBLISH_USERNAME>
                                <AEM_PUBLISH_PASSWORD>${AEM_PUBLISH_PASSWORD}</AEM_PUBLISH_PASSWORD>
                                <SELENIUM_BROWSER>${SELENIUM_BROWSER}</SELENIUM_BROWSER>
                                <HEADLESS_BROWSER>${HEADLESS_BROWSER}</HEADLESS_BROWSER>
                                <REPORTS_PATH>${REPORTS_PATH}</REPORTS_PATH>
                            </environmentVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>ui-tests-docker-build</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.6.0</version>
                        <executions>
                            <execution>
                            <id>docker-build</id>
                            <phase>package</phase>
                            <goals>
                                <goal>exec</goal>
                            </goals>
                            <configuration> 
                                <executable>docker</executable>
                                <workingDirectory>${project.basedir}</workingDirectory>
                                <arguments>
                                    <argument>build</argument>
                                    <argument>-t</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:${project.version}</argument>
                                    <argument>.</argument>
                                </arguments>
                            </configuration>
                            </execution>
                            <execution>
                            <id>docker-tag</id>
                            <phase>package</phase>
                            <goals>
                                <goal>exec</goal>
                            </goals>
                            <configuration> 
                                <executable>docker</executable>
                                <workingDirectory>${project.basedir}</workingDirectory>
                                <arguments>
                                    <argument>tag</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:${project.version}</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:latest</argument>
                                </arguments>
                            </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>ui-tests-docker-execution</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.6.0</version>
                        <executions>
                            <execution>
                                <id>docker-compose-up</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker-compose</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <environmentVariables>
                                        <AEM_AUTHOR_URL>${AEM_AUTHOR_URL}</AEM_AUTHOR_URL>
                                        <AEM_AUTHOR_USERNAME>${AEM_AUTHOR_USERNAME}</AEM_AUTHOR_USERNAME>
                                        <AEM_AUTHOR_PASSWORD>${AEM_AUTHOR_PASSWORD}</AEM_AUTHOR_PASSWORD>
                                        <AEM_PUBLISH_URL>${AEM_PUBLISH_URL}</AEM_PUBLISH_URL>
                                        <AEM_PUBLISH_USERNAME>${AEM_PUBLISH_USERNAME}</AEM_PUBLISH_USERNAME>
                                        <AEM_PUBLISH_PASSWORD>${AEM_PUBLISH_PASSWORD}</AEM_PUBLISH_PASSWORD>
                                        <SELENIUM_BROWSER>${SELENIUM_BROWSER}</SELENIUM_BROWSER>
                                        <HEADLESS_BROWSER>${HEADLESS_BROWSER}</HEADLESS_BROWSER>
                                        <REPORTS_PATH>${REPORTS_PATH}</REPORTS_PATH>
                                    </environmentVariables>
                                    <arguments>
                                        <argument>-f</argument>
                                        <argument>${project.basedir}/docker-compose-wdio-${SELENIUM_BROWSER}.yaml</argument>
                                        <argument>up</argument>
                                        <argument>--quiet-pull</argument>
                                        <argument>--abort-on-container-exit</argument>
                                        <argument>--remove-orphans</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>docker-compose-down</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker-compose</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <arguments>
                                        <argument>-f</argument>
                                        <argument>${project.basedir}/docker-compose-wdio-${SELENIUM_BROWSER}.yaml</argument>
                                        <argument>down</argument>
                                        <argument>--remove-orphans</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
