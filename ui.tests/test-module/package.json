{"name": "aem-cloud-wdio-sample-project", "version": "1.0.0", "description": "Sample Tests Project", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/adobe/aem-project-archetype.git"}, "license": "SEE LICENSE IN LICENSE file", "keywords": ["aem", "cloud", "aemaacs", "selenium", "wdio"], "devDependencies": {"@wdio/cli": "^7.1.2", "@wdio/junit-reporter": "^7.1.2", "@wdio/local-runner": "^7.1.2", "@wdio/logger": "^7.7.0", "@wdio/mocha-framework": "^7.1.2", "@wdio/reporter": "^7.9.0", "@wdio/selenium-standalone-service": "^7.1.1", "@wdio/spec-reporter": "^7.1.1", "@wdio/sync": "^7.1.1", "chai": "^4.3.4", "cross-env": "^7.0.2", "date-fns": "^2.27.0", "eslint": "^7.4.0", "eslint-plugin-wdio": "^6.0.12", "log4js": "^6.4.0", "pixelmatch": "^5.2.1", "pngjs": "^6.0.0", "request": "^2.88.2", "request-promise": "^4.2.6", "source-map-support": "^0.5.0", "tough-cookie": "^4.0.0", "wdio-html-nice-reporter": "^7.9.1", "moment": "^2.29.1"}, "scripts": {"lint": "eslint .", "pretest-local-chrome": "npm run lint", "test-local-chrome": "cross-env SELENIUM_BROWSER=chrome wdio run wdio.conf.local.js", "pretest-local-firefox": "npm run lint", "test-local-firefox": "cross-env SELENIUM_BROWSER=firefox wdio run wdio.conf.local.js", "pretest-cloud": "npm run lint", "test-cloud": "wdio run wdio.conf.cloud.js", "clean": "rm -rf node_modules reports"}}