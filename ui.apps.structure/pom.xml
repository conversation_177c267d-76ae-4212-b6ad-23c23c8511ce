<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>com.adobe.training</groupId>
        <artifactId>wetrain</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N                                   -->
    <!-- ====================================================================== -->
    <artifactId>wetrain.ui.apps.structure</artifactId>
    <packaging>content-package</packaging>
    <name>We.Train - Repository Structure Package</name>
    <description>
        Empty package that defines the structure of the Adobe Experience Manager repository the Code packages in this project deploy into.
        Any roots in the Code packages of this project should have their parent enumerated in the Filters list below.
    </description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>filevault-package-maven-plugin</artifactId>
                <configuration>
                    <properties>
                        <cloudManagerTarget>none</cloudManagerTarget>
                    </properties>
                    <filters>
                        <!-- /apps root -->
                        <filter><root>/apps</root></filter>
                        <filter><root>/apps/wetrain</root></filter>

                        <!-- Common overlay roots -->
                        <filter><root>/apps/sling</root></filter>
                        <filter><root>/apps/cq</root></filter>
                        <filter><root>/apps/dam</root></filter>
                        <filter><root>/apps/wcm</root></filter>
                        <filter><root>/apps/msm</root></filter>

                        <!-- Immutable context-aware configurations -->
                        <filter><root>/apps/settings</root></filter>

                        <!-- DAM folder root, will be created via repoinit -->
                        <filter><root>/content/dam/wetrain</root></filter>

                    </filters>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
