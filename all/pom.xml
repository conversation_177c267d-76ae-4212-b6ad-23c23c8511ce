<!--
 |  Copyright 2019 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>com.adobe.training</groupId>
        <artifactId>wetrain</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N                                   -->
    <!-- ====================================================================== -->
    <artifactId>wetrain.all</artifactId>
    <packaging>content-package</packaging>
    <name>We.Train - All</name>
    <description>All content package for We.Train</description>

    <!-- ====================================================================== -->
    <!-- B U I L D   D E F I N I T I O N                                        -->
    <!-- ====================================================================== -->
    <build>
        <plugins>
            <!-- ====================================================================== -->
            <!-- V A U L T   P A C K A G E   P L U G I N S                              -->
            <!-- ====================================================================== -->
            <plugin>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>filevault-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <group>com.adobe.training</group>
                    <packageType>container</packageType>
                    <!-- skip sub package validation for now as some vendor packages like CIF apps will not pass -->
                    <skipSubPackageValidation>true</skipSubPackageValidation>
                    <embeddeds>
                        <embedded>
                            <groupId>com.adobe.training</groupId>
                            <artifactId>wetrain.ui.apps</artifactId>
                            <type>zip</type>
                            <target>/apps/wetrain-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>com.adobe.training</groupId>
                            <artifactId>wetrain.core</artifactId>
                            <target>/apps/wetrain-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>com.adobe.training</groupId>
                            <artifactId>wetrain.ui.content</artifactId>
                            <type>zip</type>
                            <target>/apps/wetrain-packages/content/install</target>
                        </embedded>
                        <embedded>
                            <groupId>com.adobe.training</groupId>
                            <artifactId>wetrain.ui.config</artifactId>
                            <type>zip</type>
                            <target>/apps/wetrain-packages/application/install</target>
                        </embedded>
                    </embeddeds>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.day.jcr.vault</groupId>
                <artifactId>content-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <verbose>true</verbose>
                    <failOnError>true</failOnError>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
                <executions>
                    <execution>
                        <id>auto-clean</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- ====================================================================== -->
    <!-- P R O F I L E S                                                        -->
    <!-- ====================================================================== -->
    <profiles>
        <profile>
            <id>autoInstallSinglePackage</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.day.jcr.vault</groupId>
                        <artifactId>content-package-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-package</id>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>autoInstallSinglePackagePublish</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.day.jcr.vault</groupId>
                        <artifactId>content-package-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-package-publish</id>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <targetURL>http://${aem.publish.host}:${aem.publish.port}/crx/packmgr/service.jsp</targetURL>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- AEM 6.x Profile to include Core Components-->
        <profile>
            <id>classic</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.jackrabbit</groupId>
                        <artifactId>filevault-package-maven-plugin</artifactId>
                        <extensions>true</extensions>
                        <configuration>
                            <group>wetrain</group>
                            <embeddeds>
                                <embedded>
                                    <groupId>com.adobe.training</groupId>
                                    <artifactId>wetrain.ui.apps</artifactId>
                                    <type>zip</type>
                                    <target>/apps/wetrain-packages/application/install</target>
                                </embedded>
                                <embedded>
                                    <groupId>com.adobe.training</groupId>
                                    <artifactId>wetrain.core</artifactId>
                                    <target>/apps/wetrain-packages/application/install</target>
                                </embedded>
                                <embedded>
                                    <groupId>com.adobe.training</groupId>
                                    <artifactId>wetrain.ui.content</artifactId>
                                    <type>zip</type>
                                    <target>/apps/wetrain-packages/content/install</target>
                                </embedded>
                                <embedded>
                                    <groupId>com.adobe.training</groupId>
                                    <artifactId>wetrain.ui.config</artifactId>
                                    <type>zip</type>
                                    <target>/apps/wetrain-packages/application/install</target>
                                </embedded>
                                <!-- Core Components Install -->
                                <embedded>
                                    <groupId>com.adobe.cq</groupId>
                                    <artifactId>core.wcm.components.content</artifactId>
                                    <type>zip</type>
                                    <target>/apps/wetrain-vendor-packages/application/install</target>
                                </embedded>
                                <embedded>
                                    <groupId>com.adobe.cq</groupId>
                                    <artifactId>core.wcm.components.config</artifactId>
                                    <type>zip</type>
                                    <target>/apps/wetrain-vendor-packages/application/install</target>
                                </embedded>
                                <embedded>
                                    <groupId>com.adobe.cq</groupId>
                                    <artifactId>core.wcm.components.core</artifactId>
                                    <target>/apps/wetrain-vendor-packages/application/install</target>
                                </embedded>
                            </embeddeds>
                        </configuration>
                    </plugin>
                </plugins>
                </build>
            </profile>
    </profiles>

    <!-- ====================================================================== -->
    <!-- D E P E N D E N C I E S                                                -->
    <!-- ====================================================================== -->
    <dependencies>
        <dependency>
            <groupId>com.adobe.training</groupId>
            <artifactId>wetrain.ui.apps</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.training</groupId>
            <artifactId>wetrain.ui.content</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.training</groupId>
            <artifactId>wetrain.ui.config</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <!-- Core Component Dependency (6.x only)-->
        <dependency>
          <groupId>com.adobe.cq</groupId>
          <artifactId>core.wcm.components.content</artifactId>
          <version>${core.wcm.components.version}</version>
          <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>core.wcm.components.config</artifactId>
            <version>${core.wcm.components.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>core.wcm.components.core</artifactId>
            <version>${core.wcm.components.version}</version>              
        </dependency>
    </dependencies>
</project>
