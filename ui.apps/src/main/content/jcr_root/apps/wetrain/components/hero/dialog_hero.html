<!--/* This Hero component is extended from the Core Image component. 
    When testing a dialog and it's values, you can use the global
    object called 'properties' to quickly pull out values persisted
    by an authoring dialog.
    */-->
<div data-sly-use.template="core/wcm/components/commons/v1/templates.html" 
    data-sly-test.hasContent="${properties.fileReference}"
    class="">

    <!--/* Display the hero image */-->	
    <img class="" src="${properties.fileReference}"/>

    <!--/* Display a hero title and optional call to action link */-->
    <div class="">
        <h1 class="">${properties.jcr:title}</h1>
        <div class="" data-sly-test="${properties.linkText}">
            <a href="${properties.linkURL}">${properties.linkText}</a>
        </div>
    </div>
</div>

<!--/* If there is no content entered into the dialog, show a placeholder. */-->
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"></sly>