<!--/* This Hero component is extended from the Core Image component. 
    To call the sling model, you can use data-sly-use.hero
    the sling model is then stored in an object called 'hero'
    and all getters are accessible by the HTL
    */-->
<div data-sly-use.hero="com.adobe.training.core.models.Hero"
    data-sly-use.template="core/wcm/components/commons/v1/templates.html" 
    data-sly-test.hasContent="${!hero.empty}"
    data-cmp-data-layer="${hero.data.json}"
    class="cmp-hero">

    <!--/* Display the hero image */-->	
    <img class="cmp-hero__image" alt="${hero.alt}" src="${hero.src}"/>

    <!--/* Display a hero title and optional call to action link */-->
    <div class="cmp-hero__content">
        <h1 class="cmp-hero__title">${hero.title}</h1>
        <div class="cmp-hero__link" data-sly-test="${hero.linkText}">
            <a href="${hero.link}">${hero.linkText}</a>
        </div>
    </div>
</div>

<!--/* If there is no content entered into the dialog, show a placeholder. */-->
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent, classAppend='cmp-hero'}"></sly>
