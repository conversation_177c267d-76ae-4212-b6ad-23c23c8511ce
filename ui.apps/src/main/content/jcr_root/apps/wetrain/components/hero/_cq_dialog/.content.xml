<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Hero"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        granite:class="cmp-image__editor"
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <metadata
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Metadata"
                        sling:hideResource="{Boolean}true"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true"/>
                    <hero
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Hero"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <captionGroup
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/well">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <heroTitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldDescription="Additional information about the image."
                                                        fieldLabel="Hero Title"
                                                        name="./jcr:title"/>
                                                    <titleValueFromDAM
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        checked="${not empty cqDesign.titleValueFromDAM ? cqDesign.titleValueFromDAM : true}"
                                                        fieldDescription="When checked, populate the image's caption with the value of the dc:title metadata in DAM."
                                                        name="./titleValueFromDAM"
                                                        text="Get caption from DAM"
                                                        uncheckedValue="false"
                                                        value="{Boolean}true"/>
                                                    <displayPopupTitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        checked="${not empty cqDesign.displayPopupTitle ? cqDesign.displayPopupTitle : true}"
                                                        fieldDescription="When checked, the caption won't be displayed below the image, but as a pop-up displayed by some browsers when hovering over the image."
                                                        name="./displayPopupTitle"
                                                        text="Display caption as pop-up"
                                                        uncheckedValue="false"
                                                        value="{Boolean}true"/>
                                                </items>
                                            </captionGroup>
                                            <linkURL
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldDescription="Make the image a link to another resource."
                                                fieldLabel="Link"
                                                name="./linkURL"
                                                nodeTypes="dam:Asset, nt:file, cq:Page"
                                                rootPath="/content"
                                                wrapperClass="cmp-image__editor-link"/>
                                            <linkText
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Link Text"
                                                name="./linkText"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </hero>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>

