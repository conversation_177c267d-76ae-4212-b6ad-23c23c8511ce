<!--/* Sling Models not only enable headless functionality,
    but they can also be used to perform complex operations and gather
    other information for the presentation layer. This component displays
    stock information from the JCR that was imported from a 3rd party 
    source.
    */-->
<div data-sly-use.stockplex="com.adobe.training.core.models.Stockplex" 
    data-sly-use.template="core/wcm/components/commons/v1/templates.html" 
    data-sly-test.hasContent="${stockplex.symbol}" 
    data-cmp-data-layer="${stockplex.data}"
    class="cmp-stockplex">

    <div class="cmp-stockplex__column1">
        <div class="cmp-stockplex__symbol"
            data-cmp-clickable="${stockplex.data ? true : false}" >
            ${stockplex.symbol}
        </div>
        <div class="cmp-stockplex__currentPrice">${'Current Value:' @ i18n} ${stockplex.currentPrice}</div>

        <div class="cmp-stockplex__summary" data-sly-test.summary="${stockplex.summary}">
            <h3>${'Summary:' @ i18n} ${stockplex.summary}</h3>
        </div>

        <div class="cmp-stockplex__button">
            <a href="${currentPage.path @ selectors='model', extension='json'}">
                <button>${'Headless JSON View' @ i18n}</button>
            </a>
        </div>
    </div>
    <div class="cmp-stockplex__column2">
        <div class="cmp-stockplex__details" data-sly-test="${stockplex.showStockInfo}">
            <ul data-sly-list.sInfo="${stockplex.stockInfo}">
                <li class="cmp-stockplex__details-item">
                    <span class="cmp-stockplex__details-title">${sInfo @ i18n}:</span>
                    <br />
                    <span class="cmp-stockplex__details-value">${stockplex.stockInfo[sInfo]}</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- If there is no stock symbol added to the dialog, create a component placeholder -->
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent, classAppend='cmp-stockplex'}"></sly>
