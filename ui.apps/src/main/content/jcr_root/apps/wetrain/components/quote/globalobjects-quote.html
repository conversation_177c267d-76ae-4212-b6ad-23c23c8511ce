<h3>My Quote Component</h3>

<!--/* Tests to see if the author added a quote into the dialog */-->
<blockquote data-sly-test="${properties.myQuote}">
	${properties.myQuote}
</blockquote>
<p data-sly-test="${properties.myQuote}"><PERSON> <PERSON></p>

<h4>Global object docs and Javadoc</h4>
<blockquote>
    <ul>
    <li><a href="https://experienceleague.adobe.com/docs/experience-manager-htl/using/htl/global-objects.html">
        Available Global Objects in HTL</a></li>
	<li><a href="https://helpx.adobe.com/experience-manager/6-5/sites/developing/using/reference-materials/javadoc/overview-summary.html">
        Experience Manager 6.5 Javadocs</a></li>
    <li><a href="https://javadoc.io/doc/com.adobe.aem/aem-sdk-api/latest/index.html">
        Experience Manager Cloud Service Javadocs</a></li>
    </ul>
</blockquote>

<h3>Examples:</h3>
<h4>
	<a href="https://helpx.adobe.com/experience-manager/6-5/sites/developing/using/reference-materials/javadoc/org/apache/sling/api/SlingHttpServletRequest.html">Request Object API</a>
	(request)
</h4>
<blockquote>
	<p>request Path: ${request.resource.path}</p>
	<p>request resourceType: ${resource.resourceType}</p>
</blockquote>

<h4>  
	<a href="https://sling.apache.org/apidocs/sling7/org/apache/sling/api/resource/Resource.html">Resource Object API</a>
	(resource)
</h4>
<blockquote>
	<p>resource Path: ${resource.path}</p>
</blockquote>

<h4>
	<a href="https://sling.apache.org/apidocs/sling7/org/apache/sling/api/resource/ResourceResolver.html">ResourceResolver Object API</a>
	(resourceResolver)
</h4>
<blockquote>
	<p>resourceResolver Component Path: ${resourceResolver.findResource @ path=resource.resourceType}</p>
</blockquote>

<h4>
	<a href="https://helpx.adobe.com/experience-manager/6-5/sites/developing/using/reference-materials/javadoc/com/day/cq/wcm/api/Page.html">Page Object API</a>
	(currentPage)
</h4>
<blockquote>
	<p>currentPage Name: ${currentPage.Name}</p>
	<p>currentPage Title: ${currentPage.Title || "[jcr:title property not currently set]"}</p>
	<p>currentPage Parent: ${currentPage.parent.Title  || "[jcr:title property not currently set]"}</p>
	<p>currentPage Path: ${currentPage.Path}</p>
	<p>currentPage Depth: ${currentPage.Depth}</p>
</blockquote>