/*
 * This file contains designs that will be applied to 
 * the component without the style system being used.
 * The classes used here are hard coded into the component HTL script.
 * This means the template author will not have control
 * over these styles within the template and they will be 
 * applied by default.
 */


/* Example css used for validating when a client library 
 * is added to a page component 
 */
 .contentfragment {
	h1, h2, h3 {
		padding: 5px;
		border-left: medium solid #000000;
	}
}
.contentfragment p {
	font-size: 15px;
	margin: 0 20px 10px;
}