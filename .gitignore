# Created by https://www.gitignore.io/api/eclipse,java,maven

### Eclipse ###
*.pydevproject
.metadata
.gradle
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath

# Eclipse Core
.project

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# JDT-specific (Eclipse Java Development Tools)
.classpath

# Java annotation processor (APT)
.factorypath

# PDT-specific
.buildpath

# sbteclipse plugin
.target

# TeXlipse plugin
.texlipse


### Java ###
*.class

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.ear

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*


### Maven ###
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties


### Vault ###
.vlt


### IntelliJ ###
.idea/
*.iml


### Node.js ###

# Log files
*.log

# NPM
node_modules/
package-lock.json
yarn.lock

# Webpack
build/
dist/

# Frontend Maven Plugin
node/

# Tests
coverage/
reports/


LoggingFilter.java
SimpleResourceListener.java
SimpleScheduledTask.java
SimpleServlet.java
TestHelloWorldModel.java

TestHelloWorldModel.java
LoggingFilterTest.java
SimpleResourceListenerTest.java
HelloWorkdModelTest.java
SimpleScheduledTaskTest.java
SimpleServletTest.java
HelloWorldModelServerSlingTest.java
.vscode/settings.json
*.DS_Store
