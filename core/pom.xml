<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2017 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.adobe.training</groupId>
        <artifactId>wetrain</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>wetrain.core</artifactId>
    <name>We.Train - Core</name>
    <description>Core bundle for We.Train</description>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.sling</groupId>
                <artifactId>sling-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bnd-process</id>
                        <goals>
                            <goal>bnd-process</goal>
                        </goals>
                        <configuration>
                            <bnd><![CDATA[
Import-Package: javax.annotation;version=0.0.0,*
                                ]]></bnd>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-baseline-maven-plugin</artifactId>
                <configuration>
                    <failOnMissing>false</failOnMissing>
                </configuration>
                <executions>
                    <execution>
                        <id>baseline</id>
                        <goals>
                            <goal>baseline</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>uk.org.lidalia</groupId>
            <artifactId>slf4j-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>com.adobe.cq</groupId>
          <artifactId>core.wcm.components.core</artifactId>
          <version>${core.wcm.components.version}</version>  
      </dependency>


        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit-addons</groupId>
            <artifactId>junit-addons</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.wcm</groupId>
            <artifactId>io.wcm.testing.aem-mock.junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.sling</groupId>
            <artifactId>org.apache.sling.testing.caconfig-mock-plugin</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>core.wcm.components.testing.aem-mock-plugin</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Required to be able to support injection with @Self and @Via -->
        <dependency>
            <groupId>org.apache.sling</groupId>
            <artifactId>org.apache.sling.models.impl</artifactId>
            <version>1.4.4</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <!-- Additional Profiles for Cloud Service vs. 6.x -->
    <profiles>
      <profile>
          <activation>
              <activeByDefault>true</activeByDefault>
          </activation>
          <id>cloudservice</id>
          <dependencies>
              <dependency>
                  <groupId>com.adobe.aem</groupId>
                  <artifactId>aem-sdk-api</artifactId>
              </dependency>
          </dependencies>
      </profile>
      <profile>
          <id>classic</id>

           <build>
              <plugins>
                  <plugin>
                     <!--================
                      Relax AEM 6.x Dependencies to allow for broader deployment compatiblity across 6.4.x and 6.5.x
                      ====================-->
                      <groupId>biz.aQute.bnd</groupId>
                      <artifactId>bnd-maven-plugin</artifactId>
                      <executions>
                          <execution>
                              <configuration>
                                  <bnd><![CDATA[
      Import-Package: javax.annotation;version=0.0.0, \
                      com.day.cq.search;version="[1.3.0,2)", \
                      com.day.cq.wcm.api;version="[1.27.0,2)", \
                      org.apache.commons.lang3;version="[3.9.0,4)", \
                      org.apache.sling.api.resource;version="[2.11.1,3)", \
                      *
                                      ]]></bnd>
                              </configuration>
                          </execution>
                      </executions>
                  </plugin>
              </plugins>
          </build>

          <dependencies>
              <!--================
              AEM 6.x Dependencies 
              ====================-->
              <!-- Adobe AEM Dependencies -->
              <dependency>
                  <groupId>com.adobe.aem</groupId>
                  <artifactId>uber-jar</artifactId>
                  <version>6.5.13</version>
                  <scope>provided</scope>
              </dependency>
              <!-- OSGi Dependencies -->
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.annotation.versioning</artifactId>
                  <version>1.1.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.annotation.bundle</artifactId>
                  <version>1.0.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.metatype.annotations</artifactId>
                  <version>1.4.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.component.annotations</artifactId>
                  <version>1.4.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.component</artifactId>
                  <version>1.4.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.cm</artifactId>
                  <version>1.6.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.event</artifactId>
                  <version>1.3.1</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.service.log</artifactId>
                  <version>1.4.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.resource</artifactId>
                  <version>1.0.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.framework</artifactId>
                  <version>1.9.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.osgi</groupId>
                  <artifactId>org.osgi.util.tracker</artifactId>
                  <version>1.5.1</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.apache.sling</groupId>
                  <artifactId>org.apache.sling.servlets.annotations</artifactId>
                  <version>1.2.4</version>
                  <scope>provided</scope>
              </dependency>
              <!-- Logging Dependencies -->
              <dependency>
                  <groupId>org.slf4j</groupId>
                  <artifactId>slf4j-api</artifactId>
                  <version>1.7.21</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>org.slf4j</groupId>
                  <artifactId>slf4j-simple</artifactId>
                  <version>1.7.25</version>
                  <scope>test</scope>
              </dependency>
              <!-- Apache Sling Dependencies -->
              <dependency>
                  <groupId>org.apache.sling</groupId>
                  <artifactId>org.apache.sling.models.api</artifactId>
                  <version>1.3.6</version>
                  <scope>provided</scope>
              </dependency>
              <!-- Servlet API -->
              <dependency>
                  <groupId>javax.servlet</groupId>
                  <artifactId>javax.servlet-api</artifactId>
                  <version>3.1.0</version>
                  <scope>provided</scope>
              </dependency>
              <dependency>
                  <groupId>javax.servlet.jsp</groupId>
                  <artifactId>jsp-api</artifactId>
                  <version>2.1</version>
                  <scope>provided</scope>
              </dependency>

              <dependency>
                  <groupId>javax.annotation</groupId>
                  <artifactId>javax.annotation-api</artifactId>
                  <version>1.3.2</version>
                  <scope>provided</scope>
              </dependency>
              <!-- JCR -->
              <dependency>
                  <groupId>javax.jcr</groupId>
                  <artifactId>jcr</artifactId>
                  <version>2.0</version>
                  <scope>provided</scope>
              </dependency>
              <!-- Taglibs -->
              <dependency>
                  <groupId>com.day.cq.wcm</groupId>
                  <artifactId>cq-wcm-taglib</artifactId>
                  <version>5.7.4</version>
                  <scope>provided</scope>
              </dependency>
          </dependencies>
      </profile>
  </profiles>
</project>
